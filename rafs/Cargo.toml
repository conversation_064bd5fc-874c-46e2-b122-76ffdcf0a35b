[package]
name = "nydus-rafs"
version = "0.4.0"
description = "The RAFS filesystem format for Nydus Image Service"
authors = ["The Nydus Developers"]
license = "Apache-2.0 OR BSD-3-Clause"
homepage = "https://nydus.dev/"
repository = "https://github.com/dragonflyoss/nydus"
edition = "2021"

[dependencies]
anyhow = "1.0.35"
arc-swap = "1.5"
bitflags = "1.2.1"
lazy_static = "1.4.0"
libc = "0.2"
log = "0.4"
nix = "0.24"
serde = { version = "1.0.110", features = ["serde_derive", "rc"] }
serde_json = "1.0.53"
vm-memory = "0.14.1"
fuse-backend-rs = "^0.12.0"
thiserror = "1"

nydus-api = { version = "0.4.0", path = "../api" }
nydus-storage = { version = "0.7.0", path = "../storage", features = [
    "backend-localfs",
] }
nydus-utils = { version = "0.5.0", path = "../utils" }

[dev-dependencies]
vmm-sys-util = "0.12.1"
assert_matches = "1.5.0"

[features]
fusedev = ["fuse-backend-rs/fusedev"]
virtio-fs = ["fuse-backend-rs/virtiofs", "vm-memory/backend-mmap"]
vhost-user-fs = ["fuse-backend-rs/vhost-user-fs"]

[package.metadata.docs.rs]
all-features = true
targets = [
    "x86_64-unknown-linux-gnu",
    "aarch64-unknown-linux-gnu",
    "aarch64-apple-darwin",
]
