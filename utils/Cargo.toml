[package]
name = "nydus-utils"
version = "0.5.0"
description = "Utilities and helpers for Nydus Image Service"
authors = ["The Nydus Developers"]
license = "Apache-2.0 OR BSD-3-Clause"
homepage = "https://nydus.dev/"
repository = "https://github.com/dragonflyoss/nydus"
edition = "2021"

[dependencies]
thiserror = "1.0.30"
blake3 = "1.3"
httpdate = "1.0"
lazy_static = "1.4"
libc = "0.2"
log = "0.4"
lz4-sys = "1.9.4"
lz4 = "1.24.0"
openssl = { version = "0.10.48", features = ["vendored"], optional = true }
serde = { version = ">=1.0.27", features = ["serde_derive", "rc"] }
serde_json = ">=1.0.9"
sha2 = "0.10.0"
tokio = { version = "1.19.0", features = ["rt", "sync"] }
zstd = "0.11"
nix = "0.24"
crc = "3.2.1"

nydus-api = { version = "0.4.0", path = "../api" }

# libz-ng-sys doesn't compile on ppc64. Have to fallback to stock zlib-sys
[target.'cfg(target_arch = "powerpc64")'.dependencies]
libz-sys = { version = "1.1.12", features = ["stock-zlib"], default-features = false, optional = true }
flate2 = { version = "1.0.28", features = ["zlib"], default-features = false }
[target.'cfg(not(target_arch = "powerpc64"))'.dependencies]
libz-sys = { version = "1.1.8", features = ["zlib-ng"], default-features = false, optional = true }
flate2 = { version = "1.0.28", features = ["zlib-ng-compat"], default-features = false }

[dev-dependencies]
vmm-sys-util = "0.12.1"
tar = "0.4.40"

[features]
zran = ["libz-sys"]
encryption = ["openssl"]

[package.metadata.docs.rs]
all-features = true
targets = ["x86_64-unknown-linux-gnu", "aarch64-unknown-linux-gnu", "aarch64-apple-darwin"]
