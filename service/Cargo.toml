[package]
name = "nydus-service"
version = "0.4.0"
description = "Nydus Image Service Manager"
authors = ["The Nydus Developers"]
license = "Apache-2.0"
homepage = "https://nydus.dev/"
repository = "https://github.com/dragonflyoss/nydus"
edition = "2021"
resolver = "2"

[dependencies]
bytes = { version = "1", optional = true }
dbs-allocator = { version = "0.1.1", optional = true }
fuse-backend-rs = { version = "^0.12.0", features = ["persist"] }
libc = "0.2"
log = "0.4.8"
mio = { version = "0.8", features = ["os-poll", "os-ext"] }
nix = "0.24.0"
rust-fsm = "0.6.0"
serde = { version = "1.0.110", features = ["serde_derive", "rc"] }
serde_json = "1.0.51"
thiserror = "1.0"
time = { version = "0.3.14", features = ["serde-human-readable"] }
tokio = { version = "1.24", features = ["macros"] }
versionize_derive = "0.1.6"
versionize = "0.2.0"

nydus-api = { version = "0.4.0", path = "../api" }
nydus-rafs = { version = "0.4.0", path = "../rafs" }
nydus-storage = { version = "0.7.0", path = "../storage" }
nydus-upgrade = { version = "0.2.0", path = "../upgrade" }
nydus-utils = { version = "0.5.0", path = "../utils" }

vhost = { version = "0.11.0", features = ["vhost-user"], optional = true }
vhost-user-backend = { version = "0.15.0", optional = true }
virtio-bindings = { version = "0.1", features = [
    "virtio-v5_0_0",
], optional = true }
virtio-queue = { version = "0.12.0", optional = true }
vm-memory = { version = "0.14.1", features = ["backend-mmap"], optional = true }

[target.'cfg(target_os = "linux")'.dependencies]
tokio-uring = "0.4"

[dev-dependencies]
vmm-sys-util = "0.12.1"

[target.'cfg(target_os = "linux")'.dev-dependencies]
procfs = "0.17.0"

[features]
default = ["fuse-backend-rs/fusedev"]
virtiofs = [
    "fuse-backend-rs/vhost-user-fs",
    "vm-memory",
    "vhost",
    "vhost-user-backend",
    "virtio-queue",
    "virtio-bindings",
]

block-device = ["dbs-allocator", "tokio/fs"]
block-nbd = ["block-device", "bytes"]

coco = ["fuse-backend-rs/fusedev", "nydus-storage/backend-registry"]
