name: Close stale issues and PRs

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *"

permissions:
  issues: write
  pull-requests: write

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@5bef64f19d7facfb25b37b414482c7164d639639 # v9.1.0
        id: stale
        with:
          delete-branch: true
          days-before-close: 7
          days-before-stale: 60
          days-before-pr-close: 7
          days-before-pr-stale: 60
          stale-issue-label: "stale"
          exempt-issue-labels: bug,wip
          exempt-pr-labels: bug,wip
          exempt-all-milestones: true
          stale-issue-message: 'This issue is stale because it has been open 60 days with no activity.'
          close-issue-message: 'This issue was closed because it has been stalled for 7 days with no activity.'
          stale-pr-message: 'This PR is stale because it has been open 60 days with no activity.'
          close-pr-message: 'This PR was closed because it has been stalled for 7 days with no activity.'
