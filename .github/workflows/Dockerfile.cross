FROM ubuntu:latest

ENV DEBIAN_FRONTEND=noninteractive

ARG RUST_VERSION=1.84.0

RUN apt-get update && apt-get install -y \
    software-properties-common \
    build-essential \
    curl \
    git \
    libssl-dev \
    pkg-config \
    cmake \
    gcc-riscv64-linux-gnu \
    g++-riscv64-linux-gnu \
    && rm -rf /var/lib/apt/lists/*

RUN add-apt-repository ppa:ubuntu-toolchain-r/test \
    && apt-get update && apt-get install -y \
    gcc-14 \
    g++-14 \
    gcc-14-riscv64-linux-gnu \
    g++-14-riscv64-linux-gnu \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /root

RUN curl https://sh.rustup.rs -sSf | sh -s -- -y --default-toolchain $RUST_VERSION

ENV PATH="/root/.cargo/bin:${PATH}"

RUN rustup target add \
    riscv64gc-unknown-linux-gnu

RUN mkdir -p ~/.cargo && echo '\
[target.riscv64gc-unknown-linux-gnu]\n\
linker = "riscv64-linux-gnu-gcc-14"' > ~/.cargo/config.toml

CMD ["/bin/bash"]
