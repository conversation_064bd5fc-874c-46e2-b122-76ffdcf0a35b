name: Smoke Test

on:
  push:
    branches: ["**", "stable/**"]
    paths-ignore: [ '**.md', '**.png', '**.jpg', '**.svg', '**/docs/**' ]
  pull_request:
    branches: ["**", "stable/**"]
    paths-ignore: [ '**.md', '**.png', '**.jpg', '**.svg', '**/docs/**' ]
  schedule:
    # Run daily sanity check at 03:00 clock UTC
    - cron: "0 03 * * *"
  workflow_dispatch:

env:
  CARGO_TERM_COLOR: always

jobs:
  contrib-build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        arch: [amd64, arm64, ppc64le, riscv64]
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Setup Golang
      uses: actions/setup-go@v5
      with:
        go-version-file: 'go.work'
        cache-dependency-path: "**/*.sum"
    - name: Build Contrib
      run: |
        make -e DOCKER=false GOARCH=${{ matrix.arch }} contrib-release
    - name: Upload Nydusify
      if: matrix.arch == 'amd64'
      uses: actions/upload-artifact@v4
      with:
        name: nydusify-artifact
        path: contrib/nydusify/cmd

  contrib-lint:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - path: contrib/nydusify
          - path: contrib/nydus-overlayfs
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Setup Golang
      uses: actions/setup-go@v5
      with:
        go-version-file: 'go.work'
        cache: false
    - name: Lint
      uses: golangci/golangci-lint-action@v6
      with:
        version: v1.64
        working-directory: ${{ matrix.path }}
        args: --timeout=10m --verbose

  nydus-build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        arch: [amd64, arm64, ppc64le, riscv64]
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Rust Cache
      uses: Swatinem/rust-cache@v2
      with:
        cache-on-failure: true
        shared-key: ${{ runner.os }}-cargo-${{ matrix.arch }}
        save-if: ${{ github.ref == 'refs/heads/master' }}
    - uses: dsherret/rust-toolchain-file@v1
    - name: Read Rust toolchain version
      id: set_toolchain_version
      run: |
        RUST_TOOLCHAIN_VERSION=$(grep -oP '(?<=channel = ")[^"]*' rust-toolchain.toml)
        echo "Rust toolchain version: $RUST_TOOLCHAIN_VERSION"
        echo "rust-version=$RUST_TOOLCHAIN_VERSION" >> $GITHUB_OUTPUT
      shell: bash
    - name: Set up Docker Buildx
      if: matrix.arch == 'riscv64'
      uses: docker/setup-buildx-action@v3
    - name: Build and push Docker image
      if: matrix.arch == 'riscv64'
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ./.github/workflows/Dockerfile.cross
        push: false
        load: true
        tags: rust-cross-compile-riscv64:latest
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          RUST_VERSION=${{ steps.set_toolchain_version.outputs.rust-version }}
    - name: Build Nydus Non-RISC-V
      if: matrix.arch != 'riscv64'
      run: |
        declare -A rust_target_map=( ["amd64"]="x86_64-unknown-linux-musl" ["arm64"]="aarch64-unknown-linux-musl" ["ppc64le"]="powerpc64le-unknown-linux-gnu")
        RUST_TARGET=${rust_target_map[${{ matrix.arch }}]}
        cargo install --locked --version 0.2.5 cross
        make -e RUST_TARGET_STATIC=$RUST_TARGET -e CARGO=cross static-release
    - name: Build Nydus RISC-V
      if: matrix.arch == 'riscv64'
      run: |
        RUST_TARGET=riscv64gc-unknown-linux-gnu
        docker run --rm -v ${{ github.workspace }}:/root/src rust-cross-compile-riscv64:latest \
        sh -c "cd /root/src && make -e RUST_TARGET_STATIC=$RUST_TARGET static-release"
    - name: Prepare to upload artifacts
      run: |
        declare -A rust_target_map=( ["amd64"]="x86_64-unknown-linux-musl" ["arm64"]="aarch64-unknown-linux-musl" ["ppc64le"]="powerpc64le-unknown-linux-gnu" ["riscv64"]="riscv64gc-unknown-linux-gnu")
        RUST_TARGET=${rust_target_map[${{ matrix.arch }}]}
        sudo mv target/$RUST_TARGET/release/nydusd .
        sudo mv target/$RUST_TARGET/release/nydus-image .
    - name: Upload Nydus Binaries
      if: matrix.arch == 'amd64'
      uses: actions/upload-artifact@v4
      with:
        name: nydus-artifact
        path: |
          nydus-image
          nydusd

  nydusd-build-macos:
    runs-on: macos-13
    strategy:
      matrix:
        arch: [amd64, arm64]
    steps:
    - uses: actions/checkout@v4
    - name: Cache cargo
      uses: Swatinem/rust-cache@v2
      with:
        cache-on-failure: true
        shared-key: ${{ runner.os }}-cargo-${{ matrix.arch }}
        save-if: ${{ github.ref == 'refs/heads/master' }}
    - uses: dsherret/rust-toolchain-file@v1
    - name: build
      run: |
        if [[ "${{matrix.arch}}" == "amd64" ]]; then
          RUST_TARGET="x86_64-apple-darwin"
        else
          RUST_TARGET="aarch64-apple-darwin"
        fi
        cargo install --version 0.2.5 cross
        rustup target add ${RUST_TARGET}
        make -e RUST_TARGET_STATIC=$RUST_TARGET -e CARGO=cross static-release

  nydus-integration-test:
    runs-on: ubuntu-latest
    needs: [contrib-build, nydus-build]
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Docker Cache
      uses: jpribyl/action-docker-layer-caching@v0.1.0
      continue-on-error: true
    - name: Download Nydus
      uses: actions/download-artifact@v4
      with:
        name: nydus-artifact
        path: |
          target/release
    - name: Download Nydusify
      uses: actions/download-artifact@v4
      with:
        name: nydusify-artifact
        path: contrib/nydusify/cmd
    - name: Prepare Older Binaries
      id: prepare-binaries
      run: |
        export NYDUS_STABLE_VERSION=$(curl https://api.github.com/repos/Dragonflyoss/nydus/releases/latest | jq -r '.tag_name')

        versions=(v0.1.0 ${NYDUS_STABLE_VERSION})
        version_archs=(v0.1.0-x86_64 ${NYDUS_STABLE_VERSION}-linux-amd64)
        for i in ${!versions[@]}; do
          version=${versions[$i]}
          version_arch=${version_archs[$i]}

          wget -q https://github.com/dragonflyoss/nydus/releases/download/$version/nydus-static-$version_arch.tgz
          sudo mkdir nydus-$version /usr/bin/nydus-$version
          sudo tar xzf nydus-static-$version_arch.tgz -C nydus-$version
          sudo cp -r nydus-$version/nydus-static/* /usr/bin/nydus-$version/
        done
    - name: Setup Golang
      uses: actions/setup-go@v5
      with:
        go-version-file: 'go.work'
        cache-dependency-path: "**/*.sum"
    - name: Free Disk Space
      uses: jlumbroso/free-disk-space@main
      with:
        # this might remove tools that are actually needed,
        # if set to "true" but frees about 6 GB
        tool-cache: false

        # all of these default to true, but feel free to set to
        # "false" if necessary for your workflow
        android: true
        dotnet: true
        haskell: true
        large-packages: true
        docker-images: true
        swap-storage: true
    - name: Integration Test
      run: |
        sudo mkdir -p /usr/bin/nydus-latest /home/<USER>/work/workdir
        sudo install -D -m 755 contrib/nydusify/cmd/nydusify /usr/bin/nydus-latest
        sudo install -D -m 755 target/release/nydusd target/release/nydus-image /usr/bin/nydus-latest
        sudo bash misc/prepare.sh

        export NYDUS_STABLE_VERSION=$(curl https://api.github.com/repos/Dragonflyoss/nydus/releases/latest | jq -r '.tag_name')
        export NYDUS_STABLE_VERSION_EXPORT="${NYDUS_STABLE_VERSION//./_}"

        versions=(v0.1.0 ${NYDUS_STABLE_VERSION} latest)
        version_exports=(v0_1_0 ${NYDUS_STABLE_VERSION_EXPORT} latest)
        for i in ${!version_exports[@]}; do
          version=${versions[$i]}
          version_export=${version_exports[$i]}
          export NYDUS_BUILDER_$version_export=/usr/bin/nydus-$version/nydus-image
          export NYDUS_NYDUSD_$version_export=/usr/bin/nydus-$version/nydusd
          export NYDUS_NYDUSIFY_$version_export=/usr/bin/nydus-$version/nydusify
        done

        curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sudo sh -s -- -b /usr/bin v1.64.8
        sudo -E make smoke-only

  nydus-unit-test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Rust Cache
      uses: Swatinem/rust-cache@v2
      with:
        cache-on-failure: true
        shared-key: Linux-cargo-amd64
        save-if: ${{ github.ref == 'refs/heads/master' }}
    - name: Install cargo nextest
      uses: taiki-e/install-action@nextest
    - name: Fscache Setup
      run: sudo bash misc/fscache/setup.sh
    - name: Unit Test
      run: |
        CARGO_HOME=${HOME}/.cargo
        CARGO_BIN=$(which cargo)
        RUSTUP_BIN=$(which rustup)
        sudo -E RUSTUP=${RUSTUP_BIN} make ut-nextest

  contrib-unit-test-coverage:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Setup Golang
      uses: actions/setup-go@v5
      with:
        go-version-file: 'go.work'
        cache-dependency-path: "**/*.sum"
    - name: Unit Test
      run: |
        make -e DOCKER=false contrib-test
    - name: Upload contrib coverage file
      uses: actions/upload-artifact@v4
      with:
        name: contrib-test-coverage-artifact
        path: |
          contrib/nydusify/coverage.txt

  nydus-unit-test-coverage:
    runs-on: ubuntu-latest
    env:
      CARGO_TERM_COLOR: always
    steps:
      - uses: actions/checkout@v4
      - name: Rust Cache
        uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true
          shared-key: Linux-cargo-amd64
          save-if: ${{ github.ref == 'refs/heads/master' }}
      - name: Install cargo-llvm-cov
        uses: taiki-e/install-action@cargo-llvm-cov
      - name: Fscache Setup
        run: sudo bash misc/fscache/setup.sh
      - name: Generate code coverage
        run: |
          CARGO_HOME=${HOME}/.cargo
          CARGO_BIN=$(which cargo)
          RUSTUP_BIN=$(which rustup)
          sudo -E RUSTUP=${RUSTUP_BIN} make coverage-codecov
      - name: Upload nydus coverage file
        uses: actions/upload-artifact@v4
        with:
          name: nydus-test-coverage-artifact
          path: |
            codecov.json

  upload-coverage-to-codecov:
    runs-on: ubuntu-latest
    needs: [contrib-unit-test-coverage, nydus-unit-test-coverage]
    steps:
    - uses: actions/checkout@v4
    - name: Download nydus coverage file
      uses: actions/download-artifact@v4
      with:
        name: nydus-test-coverage-artifact
    - name: Download contrib coverage file
      uses: actions/download-artifact@v4
      with:
        name: contrib-test-coverage-artifact
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        files: ./codecov.json,./coverage.txt
        token: ${{ secrets.CODECOV_TOKEN }}
        verbose: true
        fail_ci_if_error: true

  nydus-cargo-deny:
    name: cargo-deny
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - uses: actions/checkout@v4
      - uses: EmbarkStudios/cargo-deny-action@v2

  performance-test:
    runs-on: ubuntu-latest
    needs: [contrib-build, nydus-build]
    strategy:
      matrix:
        include:
          - mode: fs-version-5
          - mode: fs-version-6
          - mode: zran
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download Nydus
      uses: actions/download-artifact@v4
      with:
        name: nydus-artifact
        path: target/release
    - name: Download Nydusify
      uses: actions/download-artifact@v4
      with:
        name: nydusify-artifact
        path: contrib/nydusify/cmd
    - name: Prepare Nydus Container Environment
      run: |
        sudo bash misc/prepare.sh
    - name: Performance Test
      run: |
        export PERFORMANCE_TEST_MODE=${{ matrix.mode }}
        sudo -E make smoke-performance

  takeover-test:
    runs-on: ubuntu-latest
    needs: [contrib-build, nydus-build]
    steps:
    - name: Checkout
      uses: actions/checkout@v4
    - name: Download Nydus
      uses: actions/download-artifact@v4
      with:
        name: nydus-artifact
        path: target/release
    - name: Download Nydusify
      uses: actions/download-artifact@v4
      with:
        name: nydusify-artifact
        path: contrib/nydusify/cmd
    - name: Prepare Nydus Container Environment
      run: |
        sudo bash misc/prepare.sh takeover_test
    - name: Takeover Test
      run: |
        export NEW_NYDUSD_BINARY_PATH=target/release/nydusd
        sudo -E make smoke-takeover