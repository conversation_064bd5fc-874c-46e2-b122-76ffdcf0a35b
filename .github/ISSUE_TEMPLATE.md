## Additional Information
_The following information is very important in order to help us to help you. Omission of the following details may delay your support request or receive no attention at all._

### Version of nydus being used (nydusd --version)

<!-- Example:

Version:        v2.2.0
Git Commit:     a38f6b8d6257af90d59880265335dd55fab07668
Build Time:     2023-03-01T10:05:57.267573846Z
Profile:        release
Rustc:          rustc 1.66.1 (90743e729 2023-01-10)

-->

### Version of nydus-snapshotter being used (containerd-nydus-grpc --version)

<!-- Example:

Version:     v0.5.1
Revision:    a4b21d7e93481b713ed5c620694e77abac637abb
Go version:  go1.18.6
Build time:  2023-01-28T06:05:42

-->

### Kernel information (uname -r)
_command result: uname -r_

### GNU/Linux Distribution, if applicable (cat /etc/os-release)
_command result: cat /etc/os-release_

### containerd-nydus-grpc command line used, if applicable (ps aux | grep containerd-nydus-grpc)
```
```

### client command line used, if applicable (such as: nerdctl, docker, kubectl, ctr)
```
```

### Screenshots (if applicable)

## Details about issue

