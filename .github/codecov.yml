coverage:
  status:
    project:
      default:
        enabled: yes
        target: auto  # auto compares coverage to the previous base commit
        # adjust accordingly based on how flaky your tests are
        # this allows a 0.2% drop from the previous base commit coverage
        threshold: 0.2%
    patch: false

comment:
  layout: "reach, diff, flags, files"
  behavior: default
  require_changes: true # if true: only post the comment if coverage changes

codecov:
  require_ci_to_pass: false
  notify:
    wait_for_ci: true

# When modifying this file, please validate using
# curl -X POST --data-binary @codecov.yml https://codecov.io/validate