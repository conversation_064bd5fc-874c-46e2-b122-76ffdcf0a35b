[package]
name = "nydus-rs"
# will be overridden by real git tag during cargo build
version = "0.0.0-git"
description = "Nydus Image Service"
authors = ["The Nydus Developers"]
license = "Apache-2.0 OR BSD-3-Clause"
homepage = "https://nydus.dev/"
repository = "https://github.com/dragonflyoss/nydus"
exclude = ["contrib/", "smoke/", "tests/"]
edition = "2021"
resolver = "2"
build = "build.rs"

[profile.release]
panic = "abort"

[[bin]]
name = "nydusctl"
path = "src/bin/nydusctl/main.rs"

[[bin]]
name = "nydusd"
path = "src/bin/nydusd/main.rs"

[[bin]]
name = "nydus-image"
path = "src/bin/nydus-image/main.rs"

[lib]
name = "nydus"
path = "src/lib.rs"

[dependencies]
anyhow = "1"
clap = { version = "4.0.18", features = ["derive", "cargo"] }
flexi_logger = { version = "0.25", features = ["compress"] }
fuse-backend-rs = "^0.12.0"
hex = "0.4.3"
hyper = "0.14.11"
hyperlocal = "0.8.0"
lazy_static = "1"
libc = "0.2"
log = "0.4.8"
log-panics = { version = "2.1.0", features = ["with-backtrace"] }
mio = { version = "0.8", features = ["os-poll", "os-ext"] }
nix = "0.24.0"
rlimit = "0.9.0"
rusqlite = { version = "0.30.0", features = ["bundled"] }
serde = { version = "1.0.110", features = ["serde_derive", "rc"] }
serde_json = "1.0.51"
tar = "0.4.40"
tokio = { version = "1.35.1", features = ["macros"] }

# Build static linked openssl library
openssl = { version = '0.10.72', features = ["vendored"] }

nydus-api = { version = "0.4.0", path = "api", features = [
    "error-backtrace",
    "handler",
] }
nydus-builder = { version = "0.2.0", path = "builder" }
nydus-rafs = { version = "0.4.0", path = "rafs" }
nydus-service = { version = "0.4.0", path = "service", features = [
    "block-device",
] }
nydus-storage = { version = "0.7.0", path = "storage", features = [
    "prefetch-rate-limit",
] }
nydus-utils = { version = "0.5.0", path = "utils" }

vhost = { version = "0.11.0", features = ["vhost-user"], optional = true }
vhost-user-backend = { version = "0.15.0", optional = true }
virtio-bindings = { version = "0.1", features = [
    "virtio-v5_0_0",
], optional = true }
virtio-queue = { version = "0.12.0", optional = true }
vm-memory = { version = "0.14.1", features = ["backend-mmap","backend-atomic"], optional = true }
vmm-sys-util = { version = "0.12.1", optional = true }

[build-dependencies]
time = { version = "0.3.14", features = ["formatting"] }

[dev-dependencies]
xattr = "1.0.1"
vmm-sys-util = "0.12.1"

[features]
default = [
    "fuse-backend-rs/fusedev",
    "backend-registry",
    "backend-oss",
    "backend-s3",
    "backend-http-proxy",
    "backend-localdisk",
    "dedup",
]
virtiofs = [
    "nydus-service/virtiofs",
    "vhost",
    "vhost-user-backend",
    "virtio-bindings",
    "virtio-queue",
    "vm-memory",
    "vmm-sys-util",
]
block-nbd = ["nydus-service/block-nbd"]

backend-http-proxy = ["nydus-storage/backend-http-proxy"]
backend-localdisk = [
    "nydus-storage/backend-localdisk",
    "nydus-storage/backend-localdisk-gpt",
]
backend-oss = ["nydus-storage/backend-oss"]
backend-registry = ["nydus-storage/backend-registry"]
backend-s3 = ["nydus-storage/backend-s3"]

dedup = ["nydus-storage/dedup"]

[workspace]
members = [
    "api",
    "builder",
    "clib",
    "rafs",
    "storage",
    "service",
    "upgrade",
    "utils",
]
