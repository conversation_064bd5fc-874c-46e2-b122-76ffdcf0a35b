# nydus-storage

The core storage subsystem for [Nydus Image Service](https://nydus.dev/) to:
- Fetch blob objects from storage backend such as Registry, OSS, S3, local disk and file systems etc.
- Load data from storage backend on demand.
- Cache blob objects on local storage.

## Support

**Platforms**:
- x86_64
- aarch64

**Operating Systems**:
- Linux
- MacOS

## License

This code is licensed under [Apache-2.0](LICENSE-APACHE) or [BSD-3-Clause](LICENSE-BSD-3-Clause).
