[package]
name = "nydus-storage"
version = "0.7.0"
description = "Storage subsystem for Nydus Image Service"
authors = ["The Nydus Developers"]
license = "Apache-2.0 OR BSD-3-Clause"
homepage = "https://nydus.dev/"
repository = "https://github.com/dragonflyoss/nydus"
edition = "2021"

[dependencies]
arc-swap = "1.5"
base64 = { version = "0.21", optional = true }
bitflags = "1.2.1"
hex = "0.4.3"
hmac = { version = "0.12.1", optional = true }
http = { version = "0.2.8", optional = true }
httpdate = { version = "1.0", optional = true }
hyper = { version = "0.14.11", optional = true }
hyperlocal = { version = "0.8.0", optional = true }
lazy_static = "1.4.0"
leaky-bucket = { version = "0.12.1", optional = true }
libc = "0.2"
log = "0.4.8"
nix = "0.24"
reqwest = { version = "0.11.14", features = ["blocking", "json"], optional = true }
rusqlite = { version = "0.30", features = ["bundled"], optional = true }
r2d2 = { version = "0.8", optional = true }
r2d2_sqlite = { version = "0.23", optional = true }
serde = { version = "1.0.110", features = ["serde_derive", "rc"] }
serde_json = "1.0.53"
sha1 = { version = "0.10.5", optional = true }
sha2 = { version = "0.10.2", optional = true }
tar = "0.4.40"
time = { version = "0.3.14", features = ["formatting"], optional = true }
tokio = { version = "1.19.0", features = [
    "macros",
    "rt",
    "rt-multi-thread",
    "sync",
    "time",
] }
url = { version = "2.1.1", optional = true }
vm-memory = "0.14.1"
fuse-backend-rs = "^0.12.0"
gpt = { version = "3.1.0", optional = true }

nydus-api = { version = "0.4.0", path = "../api" }
nydus-utils = { version = "0.5.0", path = "../utils", features = [
    "encryption",
    "zran",
] }

[dev-dependencies]
vmm-sys-util = "0.12.1"
tar = "0.4.40"
regex = "1.7.0"
toml = "0.5"

[features]
backend-localdisk = []
backend-localdisk-gpt = ["gpt", "backend-localdisk"]
backend-localfs = []
backend-oss = ["base64", "httpdate", "hmac", "sha1", "reqwest", "url"]
backend-registry = ["base64", "reqwest", "url"]
backend-s3 = ["base64", "hmac", "http", "reqwest", "sha2", "time", "url"]
backend-http-proxy = ["hyper", "hyperlocal", "http", "reqwest", "url"]
dedup = ["rusqlite", "r2d2", "r2d2_sqlite"]
prefetch-rate-limit = ["leaky-bucket"]

[package.metadata.docs.rs]
all-features = true
targets = [
    "x86_64-unknown-linux-gnu",
    "aarch64-unknown-linux-gnu",
    "aarch64-apple-darwin",
]
