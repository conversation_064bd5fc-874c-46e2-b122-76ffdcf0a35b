<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="766px" height="574px" viewBox="-0.5 -0.5 766 574" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-07-12T06:10:53.265Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36&quot; etag=&quot;i3GKaSylIIufyB0pJxaB&quot; version=&quot;20.0.4&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;8iMWGoXebNRQImkHv10t&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs><clipPath id="mx-clip-110-472-30-30-0"><rect x="110" y="472" width="30" height="30"/></clipPath><clipPath id="mx-clip-146-472-88-30-0"><rect x="146" y="472" width="88" height="30"/></clipPath><clipPath id="mx-clip-146-502-88-30-0"><rect x="146" y="502" width="88" height="30"/></clipPath><clipPath id="mx-clip-146-532-88-30-0"><rect x="146" y="532" width="88" height="30"/></clipPath></defs><g><rect x="100" y="422" width="570" height="150" rx="13.5" ry="13.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="392" y="1" width="370" height="350" rx="17.5" ry="17.5" fill="#eeeeee" stroke="#36393d" stroke-width="3" pointer-events="all"/><rect x="454.5" y="11" width="239" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 237px; height: 1px; padding-top: 26px; margin-left: 456px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Image 2 on Registry</div></div></div></foreignObject><text x="574" y="33" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="24px" text-anchor="middle">Image 2 on Registry</text></switch></g><rect x="1" y="1" width="370" height="350" rx="17.5" ry="17.5" fill="#eeeeee" stroke="#36393d" stroke-width="3" pointer-events="all"/><rect x="19" y="52" width="332" height="220" rx="19.8" ry="19.8" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="96" y="85" width="60" height="30" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 100px; margin-left: 97px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">/</div></div></div></foreignObject><text x="126" y="105" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">/</text></switch></g><rect x="156" y="85" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 157px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="176" y="105" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><rect x="31" y="151" width="60" height="30" fill="#60a917" stroke="#2d7600" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 166px; margin-left: 32px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">file1</div></div></div></foreignObject><text x="61" y="171" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">file1</text></switch></g><rect x="91" y="151" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 166px; margin-left: 92px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="111" y="171" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><rect x="161" y="151" width="60" height="30" fill="#0050ef" stroke="#001dbc" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 166px; margin-left: 162px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">dir1</div></div></div></foreignObject><text x="191" y="171" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">dir1</text></switch></g><rect x="221" y="151" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 166px; margin-left: 222px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="241" y="171" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><path d="M 126 115 L 69.84 146.1" fill="none" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 63.93 149.37 L 69.63 141.08 L 69.84 146.1 L 73.99 148.95 Z" fill="#001dbc" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 126 115 L 182.16 146.1" fill="none" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 188.07 149.37 L 178.01 148.95 L 182.16 146.1 L 182.37 141.08 Z" fill="#001dbc" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="97" y="222" width="60" height="30" fill="#a20025" stroke="#6f0000" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 237px; margin-left: 98px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">file2</div></div></div></foreignObject><text x="127" y="242" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">file2</text></switch></g><rect x="157" y="222" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 237px; margin-left: 158px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="177" y="242" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><rect x="231" y="222" width="60" height="30" fill="#76608a" stroke="#432d57" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 237px; margin-left: 232px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">file3</div></div></div></foreignObject><text x="261" y="242" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">file3</text></switch></g><rect x="291" y="222" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 237px; margin-left: 292px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="311" y="242" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><path d="M 191 181 L 142.36 214.28" fill="none" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 136.79 218.1 L 141.67 209.3 L 142.36 214.28 L 146.76 216.73 Z" fill="#001dbc" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 191 181 L 252.28 216.89" fill="none" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 258.11 220.3 L 248.07 219.64 L 252.28 216.89 L 252.61 211.87 Z" fill="#001dbc" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="15" y="308" width="187" height="30" fill="#cce5ff" stroke="#36393d" pointer-events="all"/><path d="M 26 309 L 46 309 L 56 324 L 46 339 L 26 339 L 16 324 Z" fill="#60a917" stroke="#2d7600" stroke-miterlimit="10" pointer-events="all"/><path d="M 66 309 L 86 309 L 96 324 L 86 339 L 66 339 L 56 324 Z" fill="#60a917" stroke="#2d7600" stroke-miterlimit="10" pointer-events="all"/><path d="M 46 181 L 31.96 300.82" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 31.26 306.78 L 28.22 298.37 L 31.96 300.82 L 36.16 299.3 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 76 181 L 76 300.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 76 306.76 L 72 298.76 L 76 300.76 L 80 298.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 106 308 L 126 308 L 136 323 L 126 338 L 106 338 L 96 323 Z" fill="#a20025" stroke="#6f0000" stroke-miterlimit="10" pointer-events="all"/><path d="M 127 252 L 117.59 299.92" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 116.43 305.81 L 114.05 297.18 L 117.59 299.92 L 121.9 298.73 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 246 252 L 241.31 299.8" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 240.72 305.77 L 237.52 297.42 L 241.31 299.8 L 245.48 298.2 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="241" y="71" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 86px; margin-left: 242px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"> Metadata</div></div></div></foreignObject><text x="271" y="92" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Metad...</text></switch></g><rect x="411" y="52" width="330" height="220" rx="15.4" ry="15.4" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all"/><rect x="488" y="85" width="60" height="30" fill="#6d8764" stroke="#3a5431" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 100px; margin-left: 489px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">/</div></div></div></foreignObject><text x="518" y="105" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">/</text></switch></g><rect x="548" y="85" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 100px; margin-left: 549px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="568" y="105" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><rect x="423" y="151" width="60" height="30" fill="#d5e8d4" stroke="#82b366" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 166px; margin-left: 424px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">file4</div></div></div></foreignObject><text x="453" y="171" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">file4</text></switch></g><rect x="483" y="151" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 166px; margin-left: 484px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="503" y="171" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><rect x="553" y="151" width="60" height="30" fill="#bac8d3" stroke="#23445d" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 166px; margin-left: 554px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">dir2</div></div></div></foreignObject><text x="583" y="171" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">dir2</text></switch></g><rect x="613" y="151" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 166px; margin-left: 614px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="633" y="171" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><path d="M 518 115 L 461.84 146.1" fill="none" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 455.93 149.37 L 461.63 141.08 L 461.84 146.1 L 465.99 148.95 Z" fill="#001dbc" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 518 115 L 574.16 146.1" fill="none" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580.07 149.37 L 570.01 148.95 L 574.16 146.1 L 574.37 141.08 Z" fill="#001dbc" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="546" y="222" width="60" height="30" fill="#a0522d" stroke="#6d1f00" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 237px; margin-left: 547px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">file5</div></div></div></foreignObject><text x="576" y="242" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">file5</text></switch></g><rect x="606" y="222" width="40" height="30" fill="#1ba1e2" stroke="#006eaf" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 237px; margin-left: 607px;"><div data-drawio-colors="color: #ffffff; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 255, 255); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">SHA</div></div></div></foreignObject><text x="626" y="242" fill="#ffffff" font-family="Helvetica" font-size="16px" text-anchor="middle">SHA</text></switch></g><path d="M 583 181 L 583.01 209.89" fill="none" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 583.02 216.64 L 578.51 207.64 L 583.01 209.89 L 587.51 207.63 Z" fill="#001dbc" stroke="#001dbc" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="633" y="71" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 86px; margin-left: 634px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Metadata</div></div></div></foreignObject><text x="663" y="92" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Metada...</text></switch></g><rect x="413" y="306" width="328" height="30" fill="#cce5ff" stroke="#36393d" pointer-events="all"/><path d="M 424 307 L 444 307 L 454 322 L 444 337 L 424 337 L 414 322 Z" fill="#60a917" stroke="#2d7600" stroke-miterlimit="10" pointer-events="all"/><path d="M 464 307 L 484 307 L 494 322 L 484 337 L 464 337 L 454 322 Z" fill="#60a917" stroke="#2d7600" stroke-miterlimit="10" pointer-events="all"/><path d="M 544 306 L 564 306 L 574 321 L 564 336 L 544 336 L 534 321 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><path d="M 584 307 L 604 307 L 614 322 L 604 337 L 584 337 L 574 322 Z" fill="#76608a" stroke="#432d57" stroke-miterlimit="10" pointer-events="all"/><path d="M 438 181 L 434.26 298.77" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 434.07 304.77 L 430.33 296.64 L 434.26 298.77 L 438.32 296.9 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 453 181 L 472.65 298.88" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 473.63 304.79 L 468.37 297.56 L 472.65 298.88 L 476.26 296.25 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 483 181 L 545.15 298.72" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 547.96 304.02 L 540.68 298.82 L 545.15 298.72 L 547.76 295.08 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 664 306 L 684 306 L 694 321 L 684 336 L 664 336 L 654 321 Z" fill="#a0522d" stroke="#6d1f00" stroke-miterlimit="10" pointer-events="all"/><path d="M 624 307 L 644 307 L 654 322 L 644 337 L 624 337 L 614 322 Z" fill="#76608a" stroke="#432d57" stroke-miterlimit="10" pointer-events="all"/><path d="M 561 252 L 589.76 299.94" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 592.85 305.08 L 585.3 300.28 L 589.76 299.94 L 592.16 296.16 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 576 252 L 628.02 301.33" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 632.38 305.46 L 623.82 302.86 L 628.02 301.33 L 629.32 297.05 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 591 252 L 667.1 301.51" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 672.13 304.78 L 663.24 303.77 L 667.1 301.51 L 667.6 297.07 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="123" y="308" width="90" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 323px; margin-left: 124px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Data 1</div></div></div></foreignObject><text x="168" y="329" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Data 1</text></switch></g><rect x="276" y="452" width="386" height="30" fill="#f9f7ed" stroke="#36393d" pointer-events="all"/><path d="M 287 453 L 307 453 L 317 468 L 307 483 L 287 483 L 277 468 Z" fill="#60a917" stroke="#2d7600" stroke-miterlimit="10" pointer-events="all"/><path d="M 327 453 L 347 453 L 357 468 L 347 483 L 327 483 L 317 468 Z" fill="#60a917" stroke="#2d7600" stroke-miterlimit="10" pointer-events="all"/><path d="M 367 452 L 387 452 L 397 467 L 387 482 L 367 482 L 357 467 Z" fill="#a20025" stroke="#6f0000" stroke-miterlimit="10" pointer-events="all"/><path d="M 407 452 L 427 452 L 437 467 L 427 482 L 407 482 L 397 467 Z" fill="#76608a" stroke="#432d57" stroke-miterlimit="10" pointer-events="all"/><path d="M 447 453 L 467 453 L 477 468 L 467 483 L 447 483 L 437 468 Z" fill="#76608a" stroke="#432d57" stroke-miterlimit="10" pointer-events="all"/><path d="M 487 452 L 507 452 L 517 467 L 507 482 L 487 482 L 477 467 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><path d="M 527 452 L 547 452 L 557 467 L 547 482 L 527 482 L 517 467 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/><path d="M 569 452 L 589 452 L 599 467 L 589 482 L 569 482 L 559 467 Z" fill="#a0522d" stroke="#6d1f00" stroke-miterlimit="10" pointer-events="all"/><path d="M 110 472 L 110 442 L 234 442 L 234 472" fill="#f9f7ed" stroke="#36393d" stroke-miterlimit="10" pointer-events="all"/><path d="M 110 472 L 110 562 L 234 562 L 234 472" fill="none" stroke="#36393d" stroke-miterlimit="10" pointer-events="none"/><path d="M 110 472 L 234 472" fill="none" stroke="#36393d" stroke-miterlimit="10" pointer-events="none"/><path d="M 140 472 L 140 502 L 140 532 L 140 562" fill="none" stroke="#36393d" stroke-miterlimit="10" pointer-events="none"/><g fill="rgb(0, 0, 0)" font-family="Helvetica" font-weight="bold" pointer-events="none" text-anchor="middle" font-size="14px"><text x="171.5" y="462.5">CAS Database</text></g><path d="M 110 472 M 234 472 M 234 502 L 110 502" fill="none" stroke="rgb(0, 0, 0)" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><rect x="110" y="472" width="30" height="30" fill="#f9f7ed" stroke="none" pointer-events="none"/><path d="M 110 472 M 140 472 M 140 502 M 110 502" fill="none" stroke="#36393d" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g fill="rgb(0, 0, 0)" font-family="Helvetica" font-weight="bold" pointer-events="none" clip-path="url(#mx-clip-110-472-30-30-0)" text-anchor="middle" font-size="9px"><text x="124.5" y="490">PK</text></g><rect x="140" y="472" width="94" height="30" fill="#f9f7ed" stroke="none" pointer-events="none"/><path d="M 140 472 M 234 472 M 234 502 M 140 502" fill="none" stroke="#36393d" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g fill="rgb(0, 0, 0)" font-family="Helvetica" font-weight="bold" text-decoration="underline" pointer-events="none" clip-path="url(#mx-clip-146-472-88-30-0)" font-size="9px"><text x="147.5" y="490">UniqueID</text></g><rect x="110" y="502" width="30" height="30" fill="#f9f7ed" stroke="none" pointer-events="none"/><path d="M 110 502 M 140 502 M 140 532 M 110 532" fill="none" stroke="#36393d" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><rect x="140" y="502" width="94" height="30" fill="#f9f7ed" stroke="none" pointer-events="none"/><path d="M 140 502 M 234 502 M 234 532 M 140 532" fill="none" stroke="#36393d" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" clip-path="url(#mx-clip-146-502-88-30-0)" font-size="9px"><text x="147.5" y="520">Row 1</text></g><rect x="110" y="532" width="30" height="30" fill="#f9f7ed" stroke="none" pointer-events="none"/><path d="M 110 532 M 140 532 M 140 562 M 110 562" fill="none" stroke="#36393d" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><rect x="140" y="532" width="94" height="30" fill="#f9f7ed" stroke="none" pointer-events="none"/><path d="M 140 532 M 234 532 M 234 562 M 140 562" fill="none" stroke="#36393d" stroke-linecap="square" stroke-miterlimit="10" pointer-events="none"/><g fill="rgb(0, 0, 0)" font-family="Helvetica" pointer-events="none" clip-path="url(#mx-clip-146-532-88-30-0)" font-size="9px"><text x="147.5" y="550">Row 2</text></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 290px; height: 1px; padding-top: 547px; margin-left: 297px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Image on Node with Local CAS</div></div></div></foreignObject><text x="442" y="553" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Image on Node with Local CAS</text></switch></g><path d="M 36 339 L 288.53 449.68" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 294.02 452.09 L 285.09 452.54 L 288.53 449.68 L 288.3 445.22 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 76 339 L 324.48 449.65" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 329.96 452.09 L 321.02 452.49 L 324.48 449.65 L 324.28 445.18 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 116 338 L 369.45 448.7" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 374.95 451.11 L 366.02 451.57 L 369.45 448.7 L 369.22 444.24 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 240.5 338 L 405.14 447.44" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 410.14 450.76 L 401.26 449.66 L 405.14 447.44 L 405.69 443 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 280.5 339 L 450.08 448.53" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 455.12 451.79 L 446.23 450.81 L 450.08 448.53 L 450.57 444.09 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 434 337 L 308.19 447.56" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 303.68 451.52 L 307.05 443.24 L 308.19 447.56 L 312.33 449.25 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 474 337 L 348.19 447.56" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 343.68 451.52 L 347.05 443.24 L 348.19 447.56 L 352.33 449.25 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 514 336 L 498.19 443.85" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 497.32 449.79 L 494.53 441.29 L 498.19 443.85 L 502.44 442.45 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 554 336 L 538.19 443.85" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 537.32 449.79 L 534.53 441.29 L 538.19 443.85 L 542.44 442.45 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 669 336 L 584.05 445.49" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 580.37 450.23 L 582.11 441.46 L 584.05 445.49 L 588.43 446.36 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 594 337 L 423.91 447.51" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 418.88 450.78 L 423.4 443.07 L 423.91 447.51 L 427.76 449.78 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 634 337 L 468.83 448.39" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 463.85 451.75 L 468.25 443.96 L 468.83 448.39 L 472.72 450.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 561 252 L 524.06 299.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 520.37 304.24 L 522.13 295.46 L 524.06 299.5 L 528.44 300.38 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 504 306 L 524 306 L 534 321 L 524 336 L 504 336 L 494 321 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="none"/><path d="M 468 181 L 511.16 298.27" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 513.23 303.9 L 506.71 297.78 L 511.16 298.27 L 514.22 295.01 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><path d="M 561 252 L 555.06 297.83" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 554.29 303.78 L 551.35 295.33 L 555.06 297.83 L 559.28 296.36 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 237px; height: 1px; padding-top: 26px; margin-left: 71px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 24px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Image 1 on Registry</div></div></div></foreignObject><text x="189" y="33" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="24px" text-anchor="middle">Image 1 on Registry</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 322px; margin-left: 676px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Data </div></div></div></foreignObject><text x="720" y="328" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Data </text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 468px; margin-left: 590px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Data </div></div></div></foreignObject><text x="634" y="474" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Data </text></switch></g><rect x="273" y="491" width="112" height="31" rx="2.79" ry="2.79" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 507px; margin-left: 274px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Image 1 Metadata</div></div></div></foreignObject><text x="329" y="510" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Image 1 Metadata</text></switch></g><rect x="401" y="491" width="112" height="31" rx="2.79" ry="2.79" fill="#dae8fc" stroke="#6c8ebf" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 507px; margin-left: 402px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Image 2 Metadata</div></div></div></foreignObject><text x="457" y="510" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Image 2 Metadata</text></switch></g><rect x="217.5" y="308" width="144" height="30" fill="#cce5ff" stroke="#36393d" pointer-events="none"/><path d="M 230.5 308 L 250.5 308 L 260.5 323 L 250.5 338 L 230.5 338 L 220.5 323 Z" fill="#76608a" stroke="#432d57" stroke-miterlimit="10" pointer-events="none"/><path d="M 270.5 309 L 290.5 309 L 300.5 324 L 290.5 339 L 270.5 339 L 260.5 324 Z" fill="#76608a" stroke="#432d57" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 324px; margin-left: 288px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;">Data 2</div></div></div></foreignObject><text x="332" y="330" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">Data 2</text></switch></g><path d="M 276 252 L 279.85 300.79" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="none"/><path d="M 280.32 306.77 L 275.71 299.11 L 279.85 300.79 L 283.68 298.48 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>