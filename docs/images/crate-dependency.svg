<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="681px" height="614px" viewBox="-0.5 -0.5 681 614" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-06-18T15:36:52.712Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.0.0 Safari/537.36&quot; etag=&quot;2qvE6-teFI-2mCzdOwnD&quot; version=&quot;20.0.1&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;bhEiEhVRONMhzZeJ_wcp&quot; name=&quot;Page-1&quot;&gt;3VtNc6M4EP01PoZCEkLiuMlkdg67VVOVqt3NkTGKYYItl5Bje3/9wiA+BMbGxiCyviRqhBDd73W3WmKBntaH34W/Df/kAYsX0A4OC/RlASFALkz/ZJJjLiEU54KViALVqRK8RP8yJbSVdBcFLNE6Ss5jGW114ZJvNmwpNZkvBN/r3d54rD91669YS/Cy9OO29O8okKGSAterLnxj0SpUj6aQ5BfWftFZvUkS+gHf10ToeYGeBOcy/299eGJxprxCL/l9XzuulhMTbCP73ICCx4f3w7fk53L17vz18UoP79GDGuXDj3fqhTfHYJc8sFR1Qs1bHgtlCL7bBCwbDyzQ4z6MJHvZ+svs6j41fyoL5TpWl9/4Rip7AjdtqycxIdmh8xVAqZgUUYyvmRTHtIu6ATpKlwpMrq2Uva9M41LVJ6xZxVUyX6FhVQ5dKSz9R+nsCv2Rlo5YkOJHNbmQIV/xjR8/V9LHSot22qr6/MH5VunuJ5PyqJTn7yTXNcsOkfwnu93CqvVau/LloEb+1TiqRtMaqcbFMR/EpqQQvOYCUAqqwX61jvXWdyaiVItMKGEiBX8vWQJLk2caOW/wVIF8J5bsjKKVG5G+WDF5CdBtAAkW+zL60OdxCg3q1u88SmdYAg/ZOvCwR/Qh8nmpuxqYKqdxO8xgB0397Xb2JAXUbZEUwClJ6sDPwtIaKwmBd2Fl08KjshSNwj7iuJbnNjw/tWw4KQdRBwd3MoqT2bHQ9aCmMIe2QyUAU7IQIZMsBDUOVoy8yEKINRJeIOCkXHN6RsShnBzmeh2zrhfeniGNb7y7O0oX6KQv8+OJXKTTmaZEs3OQmAJNV8g74SCLYSdxkNAzyxVyS56i5ygEz8lD0p4ecigZh4VF26zVhxv9U9rcaFREwKTN/3+pUF+j5+HRmNUNL0MH5EJ1GBBQx8HDPMtFdJT8yrFdC9JGioUtj06aZdGOLEv4b/NbhyJCNH1Bx3Q1CJmt2V7hfCf1oUUt4KITHUqtYcaj8zEe6R06yXxDZ2+zm42dhdf4VBmThzS73x4qDSDi7rGTYmJRu/o1KxW9w+hvQvjHWrdt1iE592A9YkNVFKngmI941xhd0KoVpH/E/McMw7SLsaYkQE5UQ2w8ZZg2WjnUGd/f08/Y0cOejt7syhgbje/XrJG6yiFwVvWQ3mY3WgRzjJpdJzvsTXZKPN30nyK8D83jOqIs8CwCq/COGuG993bt1eEd6scNkHIh44Z32BHeE8lFdqJsbvHdaeyfo0Jrxpbh0EhGf5lqgZ+EpaH68+6ifx2Hds2DSaC545f7g/F2/AwfgPP0TavCA/danRF3cUWy1stzZ8+6XOe8CWL399jU06EDcKo+r/rRXkg6Na7lktYwV25JXx0GPLv5NuOHAcdsNcqC+RmxCv0TbeaMCPMJCpDdtX3PrRUonAaeJnasZg4DjLaSHQsMI1WriE2Nmh8bPhXgeED3LO75uDovDzHSGgdjbNFaDRM0IEJviZ93Q4zRMwW25ZZ2V4hB6CxiNNtf9h+nAHVHxIx0Hs+xzwImq3m3S+ITAQZ0nNYLWjgyvXhtflGA1cyNffWDoVmuUaDlfX2TvmHbkGZd+khfFhDHArjmsxsEdXox8sRiBJ9bAjVziZvPi6TN6vvBvHv1FSZ6/g8=&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="0" y="553" width="680" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 678px; height: 1px; padding-top: 583px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydus-error</div></div></div></foreignObject><text x="340" y="588" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydus-error</text></switch></g><path d="M 60 192 L 60 372.5 L 59.2 545.78" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 59.17 551.78 L 55.21 543.77 L 59.2 545.78 L 63.21 543.8 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="132" width="120" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 162px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydus-app</div></div></div></foreignObject><text x="60" y="167" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydus-app</text></switch></g><path d="M 507 493 L 506.97 545.78" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 506.96 551.78 L 502.97 543.78 L 506.97 545.78 L 510.97 543.79 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="452" y="433" width="110" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 463px; margin-left: 453px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydus-utils</div></div></div></foreignObject><text x="507" y="468" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydus-utils</text></switch></g><path d="M 462 373 L 482 373 L 482 423 L 479.5 423 L 479.5 424.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 479.5 430.76 L 475.5 422.76 L 479.5 424.76 L 483.5 422.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 371.25 403 L 371.3 478 L 371.92 544.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 371.98 550.76 L 367.9 542.8 L 371.92 544.76 L 375.9 542.73 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="341" y="343" width="121" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 119px; height: 1px; padding-top: 373px; margin-left: 342px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydus-api</div></div></div></foreignObject><text x="402" y="378" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydus-api</text></switch></g><path d="M 227 252 L 227 388 L 332.76 388" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 338.76 388 L 330.76 392 L 332.76 388 L 330.76 384 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 197 252 L 197 463 L 443.76 463" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 449.76 463 L 441.76 467 L 443.76 463 L 441.76 459 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 257 222 L 286 222 L 286 257.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 286 263.76 L 282 255.76 L 286 257.76 L 290 255.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 167 252 L 167 402 L 166.32 543.74" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 166.29 549.74 L 162.33 541.72 L 166.32 543.74 L 170.33 541.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="137" y="192" width="120" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 222px; margin-left: 138px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydus-rafs</div></div></div></foreignObject><text x="197" y="227" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydus-rafs</text></switch></g><path d="M 415 153 L 197 153 L 197 183.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 197 189.76 L 193 181.76 L 197 183.76 L 201 181.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 415 168 L 346 168 L 346 257.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 346 263.76 L 342 255.76 L 346 257.76 L 350 255.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 520 153 L 617.8 153 L 617.8 543.74" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 617.8 549.74 L 613.8 541.74 L 617.8 543.74 L 621.8 541.74 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="415" y="123" width="105" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 103px; height: 1px; padding-top: 153px; margin-left: 416px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydus-blobfs</div></div></div></foreignObject><text x="468" y="158" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydus-blobfs</text></switch></g><path d="M 376 311 L 507 311 L 507 424.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 507 430.76 L 503 422.76 L 507 424.76 L 511 422.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 286 326 L 286 358 L 332.76 358" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 338.76 358 L 330.76 362 L 332.76 358 L 330.76 354 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 376 281 L 579.7 281 L 579.72 545.78" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 579.72 551.78 L 575.72 543.78 L 579.72 545.78 L 583.72 543.78 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="256" y="266" width="120" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 296px; margin-left: 257px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydus-storage</div></div></div></foreignObject><text x="316" y="301" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydus-storage</text></switch></g><path d="M 60 53 L 60 123.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 60 129.76 L 56 121.76 L 60 123.76 L 64 121.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 649 61.02 L 649.67 544.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 649.68 550.76 L 645.67 542.77 L 649.67 544.76 L 653.67 542.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 166.96 63 L 167 183.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 167 189.76 L 163 181.77 L 167 183.76 L 171 181.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 468 63 L 468 93 L 467.64 114.77" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 467.54 120.76 L 463.67 112.7 L 467.64 114.77 L 471.67 112.83 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 315.88 64.02 L 315.9 165 L 315.99 257.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 316 263.76 L 311.99 255.77 L 315.99 257.76 L 319.99 255.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 400.88 61.98 L 400.9 202.5 L 401.46 334.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 401.49 340.76 L 397.46 332.78 L 401.46 334.76 L 405.46 332.75 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="0" y="0" width="680" height="60" rx="9" ry="9" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 678px; height: 1px; padding-top: 30px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">nydusd</div></div></div></foreignObject><text x="340" y="35" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">nydusd</text></switch></g><path d="M 534.16 60 L 534.2 246.5 L 534.49 424.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 534.5 430.76 L 530.48 422.77 L 534.49 424.76 L 538.48 422.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>