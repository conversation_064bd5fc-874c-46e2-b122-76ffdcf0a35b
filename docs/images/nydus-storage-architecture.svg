<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="987px" height="773px" viewBox="-0.5 -0.5 987 773" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-06-19T14:03:27.168Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.0.0 Safari/537.36&quot; etag=&quot;uMFB7Ca-EYTf-lR1WZg7&quot; version=&quot;20.0.1&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;ia_WN619vWnD8JbRhgEi&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="24" y="62" width="950" height="710" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-dasharray="1 4" pointer-events="all"/><rect x="554" y="564" width="330" height="193" rx="28.95" ry="28.95" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="208" y="544" width="336" height="217" rx="32.55" ry="32.55" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="144" y="254" width="730" height="230" rx="34.5" ry="34.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="571" y="634" width="300" height="110" rx="16.5" ry="16.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><path d="M 684 40 L 684 138.5 L 652.37 138.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 647.12 138.5 L 654.12 135 L 652.37 138.5 L 654.12 142 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="624" y="0" width="240" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 238px; height: 1px; padding-top: 20px; margin-left: 625px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Rafs</div></div></div></foreignObject><text x="744" y="25" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">Rafs</text></switch></g><path d="M 780.5 159.5 L 780.5 188.03" fill="none" stroke="rgb(0, 0, 0)" stroke-width="4" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 780.5 195.53 L 775.5 185.53 L 780.5 188.03 L 785.5 185.53 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="4" stroke-miterlimit="10" pointer-events="all"/><rect x="704" y="119.5" width="153" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 151px; height: 1px; padding-top: 140px; margin-left: 705px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobDevice</div></div></div></foreignObject><text x="781" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobDevice</text></switch></g><path d="M 781 42 L 780.57 109.4" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 780.52 116.15 L 776.08 107.12 L 780.57 109.4 L 785.08 107.18 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="786" y="60" width="120" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 90px; margin-left: 788px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_new<br />async_update<br />async_read_to</div></div></div></foreignObject><text x="788" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">async_new...</text></switch></g><path d="M 780.5 240 L 780.5 300 L 780.98 349.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 781.04 356.65 L 776.45 347.69 L 780.98 349.9 L 785.45 347.6 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="703" y="200" width="155" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 153px; height: 1px; padding-top: 220px; margin-left: 704px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobDeviceIoVec</div></div></div></foreignObject><text x="781" y="225" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobDeviceIoVec</text></switch></g><rect x="786" y="166.5" width="200" height="27" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 180px; margin-left: 788px;"><div data-drawio-colors="color: #FF3399; " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(255, 51, 153); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">read_vectored_at_volatile</div></div></div></foreignObject><text x="788" y="185" fill="#FF3399" font-family="Helvetica" font-size="16px">read_vectored_at_volatile</text></switch></g><rect x="782" y="269" width="102" height="29" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 100px; height: 1px; padding-top: 284px; margin-left: 784px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_read</div></div></div></foreignObject><text x="784" y="288" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">async_read</text></switch></g><rect x="412.5" y="372" width="125" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 123px; height: 1px; padding-top: 392px; margin-left: 415px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">get_blob_object</div></div></div></foreignObject><text x="415" y="397" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">get_blob_object</text></switch></g><rect x="232" y="431" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 451px; margin-left: 233px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FsCache</div></div></div></foreignObject><text x="292" y="456" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">FsCache</text></switch></g><rect x="460" y="431" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 451px; margin-left: 461px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FileCache</div></div></div></foreignObject><text x="520" y="456" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">FileCache</text></switch></g><rect x="688" y="430" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 450px; margin-left: 689px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">DummyCache</div></div></div></foreignObject><text x="748" y="455" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">DummyCache</text></switch></g><path d="M 520 431 L 298.31 400.86" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 293.11 400.15 L 300.52 397.63 L 298.31 400.86 L 299.57 404.56 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 292 431 L 292 406.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 292 401.12 L 295.5 408.12 L 292 406.37 L 288.5 408.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 292 431 L 741.65 400.43" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 746.88 400.08 L 740.14 404.04 L 741.65 400.43 L 739.66 397.06 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 520 431 L 741.69 400.86" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 746.89 400.15 L 740.43 404.56 L 741.69 400.86 L 739.48 397.63 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 748 430 L 748 406.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 748 401.12 L 751.5 408.12 L 748 406.37 L 744.5 408.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 644 380 L 646 380 L 646 283.5 L 541.37 283.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 536.12 283.5 L 543.12 280 L 541.37 283.5 L 543.12 287 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 664 360 L 832 360 L 852 380 L 832 400 L 664 400 L 644 380 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 206px; height: 1px; padding-top: 380px; margin-left: 645px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobCache</div></div></div></foreignObject><text x="748" y="385" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobCache</text></switch></g><path d="M 207 360 L 377 360 L 397 380 L 377 400 L 207 400 L 187 380 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 208px; height: 1px; padding-top: 380px; margin-left: 188px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobObject</div></div></div></foreignObject><text x="292" y="385" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobObject</text></switch></g><path d="M 644 380 L 403.37 380" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 398.12 380 L 405.12 376.5 L 403.37 380 L 405.12 383.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 705 593 L 714.63 593" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 719.88 593 L 712.88 596.5 L 714.63 593 L 712.88 589.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 605 573 L 685 573 L 705 593 L 685 613 L 605 613 L 585 593 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 593px; margin-left: 586px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">ChunkMap</div></div></div></foreignObject><text x="645" y="598" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">ChunkMap</text></switch></g><path d="M 367 554 L 514 554 L 534 574 L 514 594 L 367 594 L 347 574 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 185px; height: 1px; padding-top: 574px; margin-left: 348px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobReader</div></div></div></foreignObject><text x="441" y="579" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobReader</text></switch></g><path d="M 644.05 486.76 L 644 555.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 644 560.88 L 640.51 553.88 L 644 555.63 L 647.51 553.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 441.11 482.62 L 441.02 533.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 441.01 540.65 L 436.52 531.64 L 441.02 533.9 L 445.52 531.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="742" y="694" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 714px; margin-left: 743px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">NoopChunkMap</div></div></div></foreignObject><text x="802" y="719" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">NoopChunkMap</text></switch></g><rect x="742" y="644" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 664px; margin-left: 743px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobChunkMap</div></div></div></foreignObject><text x="802" y="669" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobChunkMap</text></switch></g><rect x="582" y="694" width="150" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 714px; margin-left: 583px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">IndexedChunkMap</div></div></div></foreignObject><text x="657" y="719" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">IndexedChunkMap</text></switch></g><rect x="582" y="644" width="150" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 664px; margin-left: 583px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">DigestedChunkMap</div></div></div></foreignObject><text x="657" y="669" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">DigestedChunkMap</text></switch></g><path d="M 646 634 L 645.3 619.36" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 645.05 614.12 L 648.88 620.94 L 645.3 619.36 L 641.89 621.28 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="227" y="630" width="300" height="110" rx="16.5" ry="16.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="398" y="690" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 710px; margin-left: 399px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">...</div></div></div></foreignObject><text x="458" y="715" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">...</text></switch></g><rect x="398" y="640" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 660px; margin-left: 399px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OSS</div></div></div></foreignObject><text x="458" y="665" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">OSS</text></switch></g><rect x="238" y="690" width="150" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 710px; margin-left: 239px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Registry</div></div></div></foreignObject><text x="313" y="715" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">Registry</text></switch></g><rect x="238" y="640" width="150" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 660px; margin-left: 239px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">LocalFs</div></div></div></foreignObject><text x="313" y="665" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">LocalFs</text></switch></g><path d="M 440 630 L 440.41 600.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 440.48 595.12 L 443.89 602.17 L 440.41 600.37 L 436.89 602.07 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 144 369 L 92 369 L 92 547.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 92 552.88 L 88.5 545.88 L 92 547.63 L 95.5 545.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="32" y="554" width="120" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 574px; margin-left: 33px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobMetaInfo</div></div></div></foreignObject><text x="92" y="579" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobMetaInfo</text></switch></g><path d="M 152 574 L 202.64 573.18" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 207.89 573.1 L 200.95 576.71 L 202.64 573.18 L 200.83 569.71 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="93" y="484" width="200" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 198px; height: 1px; padding-top: 514px; margin-left: 95px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">get_chunks_uncompressed<br />get_chunks_compressed</div></div></div></foreignObject><text x="95" y="519" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">get_chunks_uncompressed...</text></switch></g><rect x="120" y="586" width="100" height="43" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 608px; margin-left: 122px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_read</div></div></div></foreignObject><text x="122" y="612" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">async_read</text></switch></g><rect x="449" y="497" width="100" height="43" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 98px; height: 1px; padding-top: 519px; margin-left: 451px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_read</div></div></div></foreignObject><text x="451" y="523" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">async_read</text></switch></g><rect x="420" y="0" width="110" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 108px; height: 1px; padding-top: 20px; margin-left: 421px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobFs</div></div></div></foreignObject><text x="475" y="25" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobFs</text></switch></g><path d="M 275 41 L 274.99 350.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 274.99 357.65 L 270.49 348.65 L 274.99 350.9 L 279.49 348.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><path d="M 329 41 L 329 138.5 L 534.63 138.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 539.88 138.5 L 532.88 142 L 534.63 138.5 L 532.88 135 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 302 41 L 302 272.8 L 408.63 272.75" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 413.88 272.75 L 406.88 276.25 L 408.63 272.75 L 406.88 269.25 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="248" y="1" width="108" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 106px; height: 1px; padding-top: 21px; margin-left: 249px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FsCache</div></div></div></foreignObject><text x="302" y="26" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">FsCache</text></switch></g><path d="M 530 20 L 617.63 20" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 622.88 20 L 615.88 23.5 L 617.63 20 L 615.88 16.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="530" y="11" width="98" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 96px; height: 1px; padding-top: 31px; margin-left: 532px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">fetch_range</div></div></div></foreignObject><text x="532" y="36" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">fetch_range</text></switch></g><path d="M 415 294.25 L 292 294.3 L 292 349.9" fill="none" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 292 356.65 L 287.5 347.65 L 292 349.9 L 296.5 347.65 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-miterlimit="10" pointer-events="all"/><rect x="415" y="262" width="120" height="43" rx="6.45" ry="6.45" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 284px; margin-left: 416px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">PrefetchWorker</div></div></div></foreignObject><text x="475" y="288" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">PrefetchWorker</text></switch></g><rect x="120" y="167" width="153" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-end; width: 151px; height: 1px; padding-top: 197px; margin-left: 120px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: right;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_fetch_range\<br />_uncompressed</div></div></div></foreignObject><text x="271" y="202" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="end">async_fetch_range\...</text></switch></g><rect x="304" y="159.5" width="75" height="40" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 73px; height: 1px; padding-top: 180px; margin-left: 306px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">prefetch</div></div></div></foreignObject><text x="306" y="184" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">prefetch</text></switch></g><rect x="0" y="60" width="280" height="93" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 278px; height: 1px; padding-top: 107px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 28px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Nydus Storage Subsystem</div></div></div></foreignObject><text x="140" y="115" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="28px" text-anchor="middle">Nydus Storage Subsys...</text></switch></g><rect x="45" y="705" width="80" height="25" rx="3.75" ry="3.75" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 718px; margin-left: 46px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Struct</div></div></div></foreignObject><text x="85" y="722" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">Struct</text></switch></g><path d="M 64 740 L 104 740 L 124 752.5 L 104 765 L 64 765 L 44 752.5 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 753px; margin-left: 45px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Trait</div></div></div></foreignObject><text x="84" y="757" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">Trait</text></switch></g><path d="M 43 651 L 126.63 651" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 131.88 651 L 124.88 654.5 L 126.63 651 L 124.88 647.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="48" y="620" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 635px; margin-left: 49px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">call</div></div></div></foreignObject><text x="78" y="641" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">call</text></switch></g><path d="M 43 691 L 126.63 691" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 131.88 691 L 124.88 694.5 L 126.63 691 L 124.88 687.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="48" y="661" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 676px; margin-left: 49px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 20px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">impl</div></div></div></foreignObject><text x="78" y="682" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="20px" text-anchor="middle">impl</text></switch></g><rect x="721" y="573" width="89" height="40" rx="6" ry="6" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 87px; height: 1px; padding-top: 593px; margin-left: 722px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">PersistMap</div></div></div></foreignObject><text x="766" y="598" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">PersistMap</text></switch></g><path d="M 593.5 160 L 593.5 272 L 748 272 L 748 353.63" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 748 358.88 L 744.5 351.88 L 748 353.63 L 751.5 351.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="541" y="117" width="105" height="43" rx="6.45" ry="6.45" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 103px; height: 1px; padding-top: 139px; margin-left: 542px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">BlobFactory</div></div></div></foreignObject><text x="594" y="143" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px" text-anchor="middle">BlobFactory</text></switch></g><rect x="361.5" y="115" width="181" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 179px; height: 1px; padding-top: 130px; margin-left: 364px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_new_blob_cache</div></div></div></foreignObject><text x="364" y="135" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">async_new_blob_cache</text></switch></g><rect x="536" y="284" width="181" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 179px; height: 1px; padding-top: 299px; margin-left: 538px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">send_prefetch_message</div></div></div></foreignObject><text x="538" y="304" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">send_prefetch_message</text></switch></g><rect x="593.75" y="166.5" width="65.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 64px; height: 1px; padding-top: 182px; margin-left: 596px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">create</div></div></div></foreignObject><text x="596" y="186" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">create</text></switch></g><rect x="649" y="501" width="290" height="43" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 288px; height: 1px; padding-top: 523px; margin-left: 651px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_check_ready_and_mark_pending<br />async_set_read_and_clear_pending<br />async_clear_pending</div></div></div></foreignObject><text x="651" y="527" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">async_check_ready_and_mark_pending...</text></switch></g><rect x="164" y="262.5" width="90" height="51.5" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 88px; height: 1px; padding-top: 288px; margin-left: 165px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 28px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Cache</div></div></div></foreignObject><text x="209" y="297" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="28px" text-anchor="middle">Cache</text></switch></g><rect x="225" y="554" width="122" height="45.5" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 120px; height: 1px; padding-top: 577px; margin-left: 226px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 28px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Backend</div></div></div></foreignObject><text x="286" y="585" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="28px" text-anchor="middle">Backend</text></switch></g><rect x="786" y="570.25" width="122" height="45.5" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 120px; height: 1px; padding-top: 593px; margin-left: 787px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 28px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">State</div></div></div></foreignObject><text x="847" y="601" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="28px" text-anchor="middle">State</text></switch></g><rect x="296" y="303" width="265" height="60" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 263px; height: 1px; padding-top: 333px; margin-left: 298px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: left;"><div style="display: inline-block; font-size: 16px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">async_fetch_chunks<br />async_fetch_range_compressed<br />async_fetch_range_uncompressed</div></div></div></foreignObject><text x="298" y="338" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="16px">async_fetch_chunks...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>