<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="968px" height="772px" viewBox="-0.5 -0.5 968 772" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-06-19T03:04:49.515Z&quot; agent=&quot;5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.0.0 Safari/537.36&quot; etag=&quot;TBvK4IXEGsbx_QI-4pYh&quot; version=&quot;20.0.1&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;sYNsWVG_QY7Eb84yJVCJ&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="583" y="671" width="383" height="100" fill="#e8e8e8" stroke="#cccccc" stroke-width="2" pointer-events="all"/><rect x="424.5" y="1" width="275.5" height="237" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/><rect x="1" y="1" width="407" height="237" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/><rect x="130" y="283" width="570" height="350" rx="31.5" ry="31.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="572" y="63" width="117" height="159" rx="12.87" ry="12.87" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="147" y="471" width="430" height="152" rx="16.72" ry="16.72" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/><rect x="286" y="487" width="100" height="38" rx="5.7" ry="5.7" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 506px; margin-left: 287px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Cache</div></div></div></foreignObject><text x="336" y="511" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Cache</text></switch></g><rect x="239" y="571" width="80" height="42" rx="6.3" ry="6.3" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 592px; margin-left: 240px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Registry</div></div></div></foreignObject><text x="279" y="597" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Registry</text></switch></g><rect x="469" y="487" width="100" height="38" rx="5.7" ry="5.7" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 506px; margin-left: 470px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">State</div></div></div></foreignObject><text x="519" y="511" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">State</text></switch></g><rect x="323" y="571" width="80" height="42" rx="6.3" ry="6.3" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 592px; margin-left: 324px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OSS</div></div></div></foreignObject><text x="363" y="597" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">OSS</text></switch></g><path d="M 215 613 L 215 677 L 323.4 677 L 323.4 704.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 323.4 710.76 L 319.4 702.76 L 323.4 704.76 L 327.4 702.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="155" y="571" width="80" height="42" rx="6.3" ry="6.3" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 592px; margin-left: 156px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">LocalFs</div></div></div></foreignObject><text x="195" y="597" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">LocalFs</text></switch></g><rect x="406" y="571" width="80" height="42" rx="6.3" ry="6.3" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 592px; margin-left: 407px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">P2P</div></div></div></foreignObject><text x="446" y="597" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">P2P</text></switch></g><rect x="162" y="483" width="95" height="28" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 93px; height: 1px; padding-top: 497px; margin-left: 163px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Storage</div></div></div></foreignObject><text x="210" y="503" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Storage</text></switch></g><path d="M 204 423 L 204 447 L 336 447 L 336 478.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 336 484.76 L 332 476.76 L 336 478.76 L 340 476.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="145" y="313" width="118" height="110" rx="12.1" ry="12.1" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/><rect x="175" y="323" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 338px; margin-left: 176px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">FsCached</div></div></div></foreignObject><text x="205" y="344" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">FsCach...</text></switch></g><rect x="155" y="363" width="100" height="50" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 388px; margin-left: 156px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Handler</div></div></div></foreignObject><text x="205" y="393" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Handler</text></switch></g><path d="M 335.5 423 L 335.5 455 L 335.87 478.77" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 335.97 484.76 L 331.84 476.83 L 335.87 478.77 L 339.84 476.7 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="277" y="313" width="117" height="110" rx="12.1" ry="12.1" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/><rect x="306" y="323" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 338px; margin-left: 307px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Rafs</div></div></div></foreignObject><text x="336" y="344" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Rafs</text></switch></g><rect x="286" y="363" width="100" height="50" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 388px; margin-left: 287px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FileSystem</div></div></div></foreignObject><text x="336" y="393" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">FileSystem</text></switch></g><path d="M 571 368 L 402.24 368" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 396.24 368 L 404.24 364 L 402.24 368 L 404.24 372 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 630 423 L 630 447 L 336 447 L 336 478.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 336 484.76 L 332 476.76 L 336 478.76 L 340 476.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="571" y="313" width="118" height="110" rx="12.1" ry="12.1" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" pointer-events="all"/><rect x="600" y="323" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 338px; margin-left: 601px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">BlobFs</div></div></div></foreignObject><text x="630" y="344" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">BlobFs</text></switch></g><rect x="580" y="363" width="100" height="50" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 388px; margin-left: 581px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FileSystem</div></div></div></foreignObject><text x="630" y="393" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">FileSystem</text></switch></g><rect x="489" y="571" width="80" height="42" rx="6.3" ry="6.3" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 592px; margin-left: 490px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">...</div></div></div></foreignObject><text x="529" y="597" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">...</text></switch></g><rect x="436" y="62" width="123" height="160" rx="13.53" ry="13.53" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="448" y="74" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 89px; margin-left: 449px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Guest OS</div></div></div></foreignObject><text x="498" y="95" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Guest OS</text></switch></g><rect x="578" y="165" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="602" y="175" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 603px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">VirtioFs</div></div></div></foreignObject><text x="632" y="195" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">VirtioFs</text></switch></g><rect x="578" y="115" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="603" y="125" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 140px; margin-left: 604px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">EROFS</div></div></div></foreignObject><text x="633" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">EROFS</text></switch></g><rect x="445" y="162" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="469" y="172" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 187px; margin-left: 470px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">VirtioFs</div></div></div></foreignObject><text x="499" y="192" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">VirtioFs</text></switch></g><path d="M 175.25 221 L 175.3 257 L 103 257 L 103 705.5 L 136.76 705.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="6 6" pointer-events="stroke"/><path d="M 142.76 705.5 L 134.76 709.5 L 136.76 705.5 L 134.76 701.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="146" y="61" width="117" height="160" rx="12.87" ry="12.87" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="155" y="76" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 91px; margin-left: 156px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Host OS</div></div></div></foreignObject><text x="205" y="97" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Host OS</text></switch></g><rect x="152" y="165" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="176" y="175" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 177px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FsCache</div></div></div></foreignObject><text x="206" y="195" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">FsCache</text></switch></g><rect x="152.5" y="115" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="177.5" y="125" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 140px; margin-left: 179px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">EROFS</div></div></div></foreignObject><text x="208" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">EROFS</text></switch></g><rect x="581" y="73" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 88px; margin-left: 582px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Guest OS</div></div></div></foreignObject><text x="631" y="94" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Guest OS</text></switch></g><rect x="277" y="62" width="117" height="160" rx="12.87" ry="12.87" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="288" y="75" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 90px; margin-left: 289px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Host OS</div></div></div></foreignObject><text x="338" y="96" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Host OS</text></switch></g><rect x="283" y="162" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="307" y="172" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 187px; margin-left: 308px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FUSE</div></div></div></foreignObject><text x="337" y="192" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">FUSE</text></switch></g><path d="M 204.5 221 L 204.5 267 L 204.09 304.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 204.02 310.76 L 200.11 302.72 L 204.09 304.76 L 208.11 302.81 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 335.5 222 L 335.5 304.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 335.5 310.76 L 331.5 302.76 L 335.5 304.76 L 339.5 302.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 497.5 222 L 497.5 253 L 335.5 253 L 335.5 304.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 335.5 310.76 L 331.5 302.76 L 335.5 304.76 L 339.5 302.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 630.5 222 L 630.5 267.5 L 630.09 304.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 630.02 310.76 L 626.11 302.72 L 630.09 304.76 L 634.11 302.81 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="15" y="63" width="117" height="160" rx="12.87" ry="12.87" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" pointer-events="all"/><rect x="24" y="78" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 93px; margin-left: 25px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Host OS</div></div></div></foreignObject><text x="74" y="99" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Host OS</text></switch></g><rect x="21.5" y="117" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="46.5" y="127" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 142px; margin-left: 48px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">EROFS</div></div></div></foreignObject><text x="77" y="147" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">EROFS</text></switch></g><path d="M 74 167 L 74 730.5 L 136.76 730.5" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 142.76 730.5 L 134.76 734.5 L 136.76 730.5 L 134.76 726.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="592" y="528" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 543px; margin-left: 593px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 26px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Nydusd<br style="font-size: 26px;" />on Host</div></div></div></foreignObject><text x="642" y="551" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="26px" text-anchor="middle" font-weight="bold">Nydusd...</text></switch></g><path d="M 145 693 C 145 684.72 158.43 678 175 678 C 182.96 678 190.59 679.58 196.21 682.39 C 201.84 685.21 205 689.02 205 693 L 205 743 C 205 751.28 191.57 758 175 758 C 158.43 758 145 751.28 145 743 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 205 693 C 205 701.28 191.57 708 175 708 C 158.43 708 145 701.28 145 693" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 731px; margin-left: 146px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Local<br style="font-size: 17px;" />Disk</div></div></div></foreignObject><text x="175" y="736" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">Local...</text></switch></g><path d="M 175 613 L 175 669.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 175 675.76 L 171 667.76 L 175 669.76 L 179 667.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 420 721 L 574.76 721" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 580.76 721 L 572.76 725 L 574.76 721 L 572.76 717 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 345 701 C 321 701 315 721 334.2 725 C 315 733.8 336.6 753 352.2 745 C 363 761 399 761 411 745 C 435 745 435 729 420 721 C 435 705 411 689 390 697 C 375 685 351 685 345 701 Z" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 279 613 L 280 613 L 280 661 L 345 661 L 345 692.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 345 698.76 L 341 690.76 L 345 692.76 L 349 690.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 363 613 L 363 676 L 363 680.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 363 686.76 L 359 678.76 L 363 680.76 L 367 678.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 446 613 L 446 661 L 390 661 L 390 688.76" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 390 694.76 L 386 686.76 L 390 688.76 L 394 686.76 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="439" y="16" width="261" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 259px; height: 1px; padding-top: 31px; margin-left: 440px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Kata Secure Container</div></div></div></foreignObject><text x="570" y="37" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Kata Secure Container</text></switch></g><rect x="64" y="11" width="261" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 259px; height: 1px; padding-top: 26px; margin-left: 65px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">RunC Native Container</div></div></div></foreignObject><text x="195" y="32" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">RunC Native Container</text></switch></g><rect x="155" y="527" width="215" height="42" rx="6.3" ry="6.3" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 213px; height: 1px; padding-top: 548px; margin-left: 156px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Nydus Data Blob</div></div></div></foreignObject><text x="263" y="553" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Nydus Data Blob</text></switch></g><rect x="370" y="527" width="199.5" height="42" rx="6.3" ry="6.3" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 198px; height: 1px; padding-top: 548px; margin-left: 371px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Stargz Data Blob</div></div></div></foreignObject><text x="470" y="553" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Stargz Data Blob</text></switch></g><rect x="717" y="1" width="248.5" height="237" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-dasharray="6 6" pointer-events="all"/><rect x="730" y="63" width="224.5" height="159" rx="17.49" ry="17.49" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="843.5" y="115" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="868.5" y="125" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 140px; margin-left: 870px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">EROFS</div></div></div></foreignObject><text x="899" y="145" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">EROFS</text></switch></g><rect x="785" y="71" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 86px; margin-left: 786px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Guest OS</div></div></div></foreignObject><text x="835" y="92" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Guest OS</text></switch></g><rect x="730.5" y="16" width="222.5" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 221px; height: 1px; padding-top: 31px; margin-left: 732px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Kata Confidential Container</div></div></div></foreignObject><text x="842" y="37" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Kata Confidential Con...</text></switch></g><rect x="843.5" y="165" width="105" height="50" rx="5.5" ry="5.5" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="867.5" y="175" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 190px; margin-left: 869px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">FsCache</div></div></div></foreignObject><text x="898" y="195" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">FsCache</text></switch></g><rect x="738.5" y="117" width="105" height="94" rx="10.34" ry="10.34" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><rect x="761" y="147" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 162px; margin-left: 762px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 18px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Nydusd<br />in Guest</div></div></div></foreignObject><text x="791" y="167" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="18px" text-anchor="middle">Nydusd...</text></switch></g><path d="M 796 328.5 L 748 328.5 L 708.24 328.79" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 702.24 328.83 L 710.21 324.78 L 708.24 328.79 L 710.26 332.78 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="796" y="294" width="170" height="69" rx="7.59" ry="7.59" fill="#ededed" stroke="#cccccc" stroke-width="3" pointer-events="all"/><rect x="837.75" y="311" width="100" height="30" fill="#ededed" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 326px; margin-left: 839px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Containerd Snapshotter</div></div></div></foreignObject><text x="888" y="332" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Containerd...</text></switch></g><path d="M 796 500.5 L 748 500.5 L 707.1 500.08" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 701.1 500.02 L 709.14 496.1 L 707.1 500.08 L 709.05 504.1 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="796" y="466" width="170" height="69" rx="7.59" ry="7.59" fill="#ededed" stroke="#cccccc" stroke-width="3" pointer-events="all"/><rect x="837.75" y="483" width="100" height="30" fill="#ededed" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 498px; margin-left: 839px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">NPM</div></div></div></foreignObject><text x="888" y="504" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">NPM</text></switch></g><path d="M 796.5 585.5 L 749.8 585.5 L 711.24 585.91" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 705.24 585.98 L 713.19 581.89 L 711.24 585.91 L 713.28 589.89 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="796.5" y="551" width="170" height="69" rx="7.59" ry="7.59" fill="#ededed" stroke="#cccccc" stroke-width="3" pointer-events="all"/><rect x="838.25" y="568" width="100" height="30" fill="#ededed" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 583px; margin-left: 839px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Sealer</div></div></div></foreignObject><text x="888" y="589" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Sealer</text></switch></g><path d="M 795.5 415.5 L 749.3 415.5 L 711.24 415.91" fill="none" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 705.24 415.98 L 713.19 411.89 L 711.24 415.91 L 713.28 419.89 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><rect x="795.5" y="381" width="170" height="69" rx="7.59" ry="7.59" fill="#ededed" stroke="#cccccc" stroke-width="3" stroke-dasharray="9 9" pointer-events="all"/><rect x="837.25" y="398" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 413px; margin-left: 838px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 21px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; overflow-wrap: normal;">Docker<br />GraphDriver</div></div></div></foreignObject><text x="887" y="419" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="21px" text-anchor="middle" font-weight="bold">Docker...</text></switch></g><path d="M 745 697 C 745 688.72 758.43 682 775 682 C 782.96 682 790.59 683.58 796.21 686.39 C 801.84 689.21 805 693.02 805 697 L 805 747 C 805 755.28 791.57 762 775 762 C 758.43 762 745 755.28 745 747 Z" fill="#e8e8e8" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 805 697 C 805 705.28 791.57 712 775 712 C 758.43 712 745 705.28 745 697" fill="none" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 735px; margin-left: 746px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">NAS</div></div></div></foreignObject><text x="775" y="740" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">NAS</text></switch></g><path d="M 668 697 C 668 688.72 681.43 682 698 682 C 705.96 682 713.59 683.58 719.21 686.39 C 724.84 689.21 728 693.02 728 697 L 728 747 C 728 755.28 714.57 762 698 762 C 681.43 762 668 755.28 668 747 Z" fill="#e8e8e8" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 728 697 C 728 705.28 714.57 712 698 712 C 681.43 712 668 705.28 668 697" fill="none" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 735px; margin-left: 669px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">OSS</div></div></div></foreignObject><text x="698" y="740" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">OSS</text></switch></g><path d="M 819 697 C 819 688.72 832.43 682 849 682 C 856.96 682 864.59 683.58 870.21 686.39 C 875.84 689.21 879 693.02 879 697 L 879 747 C 879 755.28 865.57 762 849 762 C 832.43 762 819 755.28 819 747 Z" fill="#e8e8e8" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 879 697 C 879 705.28 865.57 712 849 712 C 832.43 712 819 705.28 819 697" fill="none" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 735px; margin-left: 820px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">CEPH</div></div></div></foreignObject><text x="849" y="740" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">CEPH</text></switch></g><path d="M 893.25 697 C 893.25 688.72 906.68 682 923.25 682 C 931.21 682 938.84 683.58 944.46 686.39 C 950.09 689.21 953.25 693.02 953.25 697 L 953.25 747 C 953.25 755.28 939.82 762 923.25 762 C 906.68 762 893.25 755.28 893.25 747 Z" fill="#e8e8e8" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 953.25 697 C 953.25 705.28 939.82 712 923.25 712 C 906.68 712 893.25 705.28 893.25 697" fill="none" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 735px; margin-left: 894px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Gluster</div></div></div></foreignObject><text x="923" y="740" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">Gluster</text></switch></g><path d="M 595 697 C 595 688.72 608.43 682 625 682 C 632.96 682 640.59 683.58 646.21 686.39 C 651.84 689.21 655 693.02 655 697 L 655 747 C 655 755.28 641.57 762 625 762 C 608.43 762 595 755.28 595 747 Z" fill="#e8e8e8" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><path d="M 655 697 C 655 705.28 641.57 712 625 712 C 608.43 712 595 705.28 595 697" fill="none" stroke="#cccccc" stroke-width="2" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 735px; margin-left: 596px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 17px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Dragon<br />Fly</div></div></div></foreignObject><text x="625" y="740" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="17px" text-anchor="middle">Dragon...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>